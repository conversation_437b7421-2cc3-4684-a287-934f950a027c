using MySqlConnector;

string connectionString = "Server=localhost,3306;User Id=myac;Password=*****;Database=myadaptivecloud;";

try
{
    using var connection = new MySqlConnection(connectionString);
    connection.Open();
    Console.WriteLine("🔧 Fixing ***** user email address...");
    Console.WriteLine();

    // Check current state
    var checkCmd = new MySqlCommand("SELECT UserId, Email FROM User WHERE Email IN ('<EMAIL>', '*****@localhost')", connection);
    using var reader = checkCmd.ExecuteReader();
    
    Console.WriteLine("Current users:");
    while (reader.Read())
    {
        Console.WriteLine($"   UserId: {reader["UserId"]}, Email: {reader["Email"]}");
    }
    reader.Close();
    
    Console.WriteLine();
    
    // Apply the migration from MYAC-1093.sql
    var updateCmd = new MySqlCommand("UPDATE User SET Email = '*****@localhost' WHERE Email = '<EMAIL>'", connection);
    int rowsAffected = updateCmd.ExecuteNonQuery();
    
    if (rowsAffected > 0)
    {
        Console.WriteLine($"✅ Updated {rowsAffected} user(s) from <EMAIL> to *****@localhost");
    }
    else
    {
        Console.WriteLine("❌ No rows were updated. User might already be *****@localhost or not exist.");
    }
    
    Console.WriteLine();
    
    // Verify the change
    var verifyCmd = new MySqlCommand("SELECT UserId, Email FROM User WHERE Email IN ('<EMAIL>', '*****@localhost')", connection);
    using var reader2 = verifyCmd.ExecuteReader();
    
    Console.WriteLine("After migration:");
    while (reader2.Read())
    {
        Console.WriteLine($"   UserId: {reader2["UserId"]}, Email: {reader2["Email"]}");
    }
    reader2.Close();
    
    Console.WriteLine();
    Console.WriteLine("🎉 Migration complete!");
    Console.WriteLine();
    Console.WriteLine("You can now login with:");
    Console.WriteLine("   Username: *****@localhost");
    Console.WriteLine("   Password: 123456");
}
catch (Exception ex)
{
    Console.WriteLine($"❌ Error: {ex.Message}");
}

Console.WriteLine();
Console.WriteLine("Press any key to exit...");
Console.ReadKey();
