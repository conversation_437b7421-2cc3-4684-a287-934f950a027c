-- <PERSON><PERSON><PERSON> to recreate the MyAdaptiveCloud database for fresh development setup
-- Run this script as a database administrator (root user)

-- Drop the existing database (WARNING: This will delete ALL data!)
DROP DATABASE IF EXISTS myadaptivecloud;

-- Create a new empty database with proper settings for MariaDB
CREATE DATABASE myadaptivecloud
    CHARACTER SET utf8mb4
    COLLATE utf8mb4_unicode_ci;

-- Optional: Show confirmation that database was created
SHOW DATABASES LIKE 'myadaptivecloud';

-- Optional: Show the database character set and collation
SELECT 
    SCHEMA_NAME as 'Database',
    DEFAULT_CHARACTER_SET_NAME as 'Character Set',
    DEFAULT_COLLATION_NAME as 'Collation'
FROM information_schema.SCHEMATA 
WHERE SCHEMA_NAME = 'myadaptivecloud';
