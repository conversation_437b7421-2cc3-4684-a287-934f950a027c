-- liquibase formatted sql

-- changeset voviedo:537ce8e7-0557-475d-8e1e-b297f66d05ee context:main
CALL RemoveConfigurationValue('Agent', 'HeartbeatAlertThresholdSeconds');
CALL RemoveConfigurationValue('Agent', 'CpuUsageWarningThreshold');
CALL RemoveConfigurationValue('Agent', 'CpuUsageCriticalThreshold');
CALL RemoveConfigurationValue('Agent', 'RamUsageWarningThreshold');
CALL RemoveConfigurationValue('Agent', 'RamUsageCriticalThreshold');
CALL RemoveConfigurationValue('Agent', 'DiskUsageWarningThreshold');
CALL RemoveConfigurationValue('Agent', 'DiskUsageCriticalThreshold');
CALL RemoveConfigurationValue('Feature Flags', 'DeviceFolders');
CALL RemoveConfigurationValue('Feature Flags', 'DeviceAlerts');
CALL RemoveConfigurationValue('Feature Flags', 'AgentPolicies');
CALL RemoveConfigurationValue('Feature Flags', 'FileAdministration');
CALL RemoveConfigurationValue('Feature Flags', 'InvitationCode');
CALL RemoveConfigurationValue('Feature Flags', 'MyDevices');
CALL RemoveConfigurationValue('Feature Flags', 'APIUser');

-- changeset voviedo:2455d93f-154c-43a7-a50a-38756ca3fdc2 context:main
SELECT configurationId INTO @websiteConfigurationId FROM Configuration WHERE Category = 'website';
DELETE FROM ConfigurationValues WHERE ConfigurationId = @websiteConfigurationId;
DELETE FROM Configuration WHERE ConfigurationId = @websiteConfigurationId;

-- changeset lcerbino:5ce4ce64-0b3e-47c1-98c8-9fe50ee8ba37 context:main
CREATE TABLE IF NOT EXISTS `configuration_value_organization` (
    `ConfigurationValueOrganizationId` INT NOT NULL AUTO_INCREMENT,
    `Value` VARCHAR(65000) NOT NULL COLLATE 'latin1_swedish_ci',
    `OrganizationId` INT NOT NULL,
    `CreatedBy` INT NOT NULL,
    `UpdatedBy` INT,
    `CreatedOn` DATETIME NOT NULL,
    `UpdatedOn` DATETIME,
    `ConfigurationValuesId` INT NOT NULL,
    PRIMARY KEY (`ConfigurationValueOrganizationId`),
    UNIQUE INDEX `OrganizationId_ConfigurationValuesId` (`OrganizationId`, `ConfigurationValuesId`),
    CONSTRAINT `fk_configuration_value_organization_organizationId` FOREIGN KEY (`OrganizationId`) REFERENCES `Organization` (`OrganizationId`),
    CONSTRAINT `fk_configuration_value_organization_configuration_value_id` FOREIGN KEY (`ConfigurationValuesId`) REFERENCES `ConfigurationValues` (`ConfigurationValuesId`)
);


-- changeset lcerbino:7d60baa4-c158-4b2b-bf59-5ac9c39afa3b context:main
-- Ensure admin user exists for the main context (required by stored procedures)
INSERT IGNORE INTO User (FirstName, Email, LastName, Password, CreatedDate, IsActive)
VALUES ('Local Admin', 'admin@localhost', 'Local Admin', '$2a$11$MXMwGFZ.spue6PHWTxn6YOxPvrqrbrhQU33/uExsWO6clWNKI6s/y', NOW(), 1);

-- Get the default user ID, with fallback to the first available user if admin@localhost doesn't exist
SELECT COALESCE(
    (SELECT UserId FROM User WHERE Email = 'admin@localhost' LIMIT 1),
    (SELECT UserId FROM User WHERE Email = '<EMAIL>' LIMIT 1),
    (SELECT MIN(UserId) FROM User WHERE UserId IS NOT NULL)
) INTO @defaultUserId;

-- Ensure a root organization exists for the main context
-- First, try to create with auto-increment, then update to ID 0 if needed
INSERT IGNORE INTO Organization (Name, CreatedDate, IsActive, AllowSubOrg, AllowWhiteLabel, IsPartner, IsVerified, ParentOrganizationId)
SELECT 'Root', NOW(), 1, 1, 1, 0, 1, NULL
WHERE NOT EXISTS (SELECT 1 FROM Organization WHERE OrganizationId = 0);

-- If we just created a new organization and there's no organization with ID 0, update it
UPDATE Organization SET OrganizationId = 0
WHERE Name = 'Root' AND OrganizationId != 0
AND NOT EXISTS (SELECT 1 FROM (SELECT * FROM Organization) AS o WHERE o.OrganizationId = 0);

-- Get the default organization ID, with fallback to the first available organization
SELECT COALESCE(
    (SELECT OrganizationId FROM Organization WHERE OrganizationId = 0 LIMIT 1),
    (SELECT MIN(OrganizationId) FROM Organization WHERE OrganizationId IS NOT NULL),
    0
) INTO @defaultOrgId;

INSERT INTO configuration_value_organization (ConfigurationValuesId, OrganizationId, Value, CreatedBy, UpdatedBy, CreatedOn, UpdatedOn)
SELECT
    ConfigurationValuesId,
    @defaultOrgId AS OrganizationId,
    Value,
    COALESCE(UpdatedBy, @defaultUserId, 1) AS CreatedBy,
    UpdatedBy,
    COALESCE(UpdatedDate, UTC_TIMESTAMP()) AS CreatedOn,
    UpdatedDate
FROM
    ConfigurationValues;


 -- changeset jbarrera:081A7562-27EA-4C0E-B6F1-0B3CB94843A2 context:main
 UPDATE ConfigurationValues SET InputType = 'toggle' WHERE Value = 'true' OR Value = 'false'; 

 -- changeset voviedo:b3a60d74-de1c-4e32-81bd-6e178257f4e4 context:main
 ALTER TABLE IF EXISTS `ConfigurationValues` DROP COLUMN IF EXISTS `Value`;
 ALTER TABLE IF EXISTS `ConfigurationValues` DROP COLUMN IF EXISTS `UpdatedBy`;
 ALTER TABLE IF EXISTS `ConfigurationValues` DROP COLUMN IF EXISTS `UpdatedDate`;