-- liquibase formatted sql

-- changeset ggoodrich:c55731c7-2893-4f1b-8f6c-9ea80c319717
-- It seems this table was left hanging around in Production after migrating the data to OrganizationMapping, so cleaning it up here
DROP TABLE IF EXISTS AC_Organization_Mapping;

-- changeset jbarrera:D211B594-01A8-4DC2-9322-774827F421A6
CREATE UNIQUE INDEX
IF NOT EXISTS Category ON Configuration
(Category);
ALTER TABLE
IF EXISTS ConfigurationValues MODIFY COLUMN
IF EXISTS Value TEXT NOT NULL;

-- changeset ggoodrich:e5a5368b-1e6d-4fba-9c0b-8e3936082f73
ALTER TABLE Organization
    DROP COLUMN IF EXISTS UserIP
,
DROP COLUMN
IF EXISTS SecurityCode,
DROP COLUMN
IF EXISTS IsEnrolled2fa,
ADD COLUMN
IF NOT EXISTS IsVerified TINYINT
(1) NULL,
ADD COLUMN
IF NOT EXISTS IsPartner TINYINT
(1) NULL;

UPDATE Organization SET IsPartner = AllowSubOrg WHERE IsPartner IS NULL;
UPDATE Organization SET IsVerified = 1 WHERE IsVerified IS NULL;

ALTER TABLE Organization
    MODIFY COLUMN IsVerified TINYINT
(1) NOT NULL DEFAULT '0',
    MODIFY COLUMN IsPartner TINYINT
(1) NOT NULL DEFAULT '0';