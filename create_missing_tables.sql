-- Script to create essential missing tables for MyAdaptiveCloud authentication to work
-- This is a temporary fix to get the application running after database recreation

-- Create configuration_value_organization table
CREATE TABLE IF NOT EXISTS `configuration_value_organization` (
    `ConfigurationValueOrganizationId` INT NOT NULL AUTO_INCREMENT,
    `Value` VARCHAR(65000) NOT NULL COLLATE 'latin1_swedish_ci',
    `OrganizationId` INT NOT NULL,
    `CreatedBy` INT NOT NULL,
    `UpdatedBy` INT,
    `CreatedOn` DATETIME NOT NULL,
    `UpdatedOn` DATETIME,
    `ConfigurationValuesId` INT NOT NULL,
    PRIMARY KEY (`ConfigurationValueOrganizationId`),
    UNIQUE INDEX `OrganizationId_ConfigurationValuesId` (`OrganizationId`, `ConfigurationValuesId`),
    CONSTRAINT `fk_configuration_value_organization_organizationId` FOREIGN KEY (`OrganizationId`) REFERENCES `Organization` (`OrganizationId`),
    CONSTRAINT `fk_configuration_value_organization_configurationValuesId` FOREIGN KEY (`ConfigurationValuesId`) REFERENCES `ConfigurationValues` (`ConfigurationValuesId`)
);

-- Get the default user ID, with fallback to the first available user if admin@localhost doesn't exist
SELECT COALESCE(
    (SELECT UserId FROM User WHERE Email = 'admin@localhost' LIMIT 1),
    (SELECT UserId FROM User WHERE Email = '<EMAIL>' LIMIT 1),
    (SELECT MIN(UserId) FROM User WHERE UserId IS NOT NULL)
) INTO @defaultUserId;

-- Get the default organization ID, with fallback to the first available organization
SELECT COALESCE(
    (SELECT OrganizationId FROM Organization WHERE OrganizationId = 0 LIMIT 1),
    (SELECT MIN(OrganizationId) FROM Organization WHERE OrganizationId IS NOT NULL),
    0
) INTO @defaultOrgId;

-- Populate configuration_value_organization with existing ConfigurationValues
INSERT IGNORE INTO configuration_value_organization (ConfigurationValuesId, OrganizationId, Value, CreatedBy, UpdatedBy, CreatedOn, UpdatedOn)
SELECT
    ConfigurationValuesId,
    @defaultOrgId AS OrganizationId,
    Value,
    COALESCE(UpdatedBy, @defaultUserId, 1) AS CreatedBy,
    UpdatedBy,
    COALESCE(UpdatedDate, UTC_TIMESTAMP()) AS CreatedOn,
    UpdatedDate
FROM
    ConfigurationValues;

-- Show results
SELECT 'Configuration Value Organization table created and populated' AS Status;
SELECT COUNT(*) AS 'Records inserted' FROM configuration_value_organization;
