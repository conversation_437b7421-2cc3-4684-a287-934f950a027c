-- liquibase formatted sql

-- changeset update_invitation_expiration:extend-invitation-code-A2354-154458S-0 context:dev,test
-- Update expiration date for invitation code 'A2354-154458S-0' to extend it by 10 years
UPDATE `invitation_code` 
SET `expiration_date` = '2035-09-02 01:07:33',
    `updated_on` = NOW(),
    `updated_by` = (SELECT UserId FROM User WHERE Email = 'admin@localhost' LIMIT 1)
WHERE `code` = 'A2354-154458S-0';

-- Verify the update
SELECT 
    `invitation_code_id`,
    `code`,
    `expiration_date`,
    `updated_on`,
    `updated_by`
FROM `invitation_code` 
WHERE `code` = 'A2354-154458S-0';
