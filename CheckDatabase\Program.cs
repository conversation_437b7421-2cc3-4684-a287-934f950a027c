using MySqlConnector;
using BCrypt.Net;

string connectionString = "Server=localhost,3306;User Id=myac;Password=*****;Database=myadaptivecloud;";

Console.WriteLine("🔍 MyAdaptiveCloud Authentication Diagnostic Tool");
Console.WriteLine("================================================");
Console.WriteLine();

try
{
    using var connection = new MySqlConnection(connectionString);
    connection.Open();
    Console.WriteLine("✅ Database connection successful!");
    Console.WriteLine($"Connected to: {connectionString.Replace("Password=*****", "Password=***")}");
    Console.WriteLine();

    // Check for *****@localhost user
    var checkAdminLocalhost = new MySqlCommand("SELECT UserId, FirstName, LastName, Email, Password FROM User WHERE Email = '*****@localhost'", connection);
    using var reader1 = checkAdminLocalhost.ExecuteReader();

    Console.WriteLine("=== *****@localhost user ===");
    if (reader1.Read())
    {
        Console.WriteLine($"✅ User found!");
        Console.WriteLine($"   UserId: {reader1["UserId"]}");
        Console.WriteLine($"   Name: {reader1["FirstName"]} {reader1["LastName"]}");
        Console.WriteLine($"   Email: {reader1["Email"]}");
        Console.WriteLine($"   Password Hash: {reader1["Password"]}");

        string storedHash = reader1["Password"].ToString();

        // Test the specific password you're trying
        Console.WriteLine();
        Console.WriteLine("🔐 Testing your credentials (*****@localhost / 123456):");
        bool isValid123456 = BCrypt.Net.BCrypt.Verify("123456", storedHash);
        Console.WriteLine($"   Password '123456': {(isValid123456 ? "✅ MATCH - This should work!" : "❌ No match")}");

        // Test other common passwords
        string[] testPasswords = { "*****", "password", "localhost", "*****123", "test", "" };

        Console.WriteLine();
        Console.WriteLine("🔐 Testing other common passwords:");
        foreach (string testPassword in testPasswords)
        {
            bool isValid = BCrypt.Net.BCrypt.Verify(testPassword, storedHash);
            Console.WriteLine($"   '{testPassword}': {(isValid ? "✅ MATCH" : "❌ No match")}");
        }
    }
    else
    {
        Console.WriteLine("❌ *****@localhost user not found!");
    }
    reader1.Close();

    Console.WriteLine();
    Console.WriteLine("=== <EMAIL> user ===");

    // <NAME_EMAIL> user
    var checkAdminGmail = new MySqlCommand("SELECT UserId, FirstName, LastName, Email, Password FROM User WHERE Email = '<EMAIL>'", connection);
    using var reader2 = checkAdminGmail.ExecuteReader();

    if (reader2.Read())
    {
        Console.WriteLine($"✅ User found!");
        Console.WriteLine($"   UserId: {reader2["UserId"]}");
        Console.WriteLine($"   Name: {reader2["FirstName"]} {reader2["LastName"]}");
        Console.WriteLine($"   Email: {reader2["Email"]}");
        Console.WriteLine($"   Password Hash: {reader2["Password"]}");

        string storedHash = reader2["Password"].ToString();

        // Test the specific password you're trying
        Console.WriteLine();
        Console.WriteLine("🔐 Testing common passwords:");
        string[] testPasswords = { "*****", "123456", "password", "localhost", "*****123" };

        foreach (string testPassword in testPasswords)
        {
            bool isValid = BCrypt.Net.BCrypt.Verify(testPassword, storedHash);
            Console.WriteLine($"   '{testPassword}': {(isValid ? "✅ MATCH" : "❌ No match")}");
        }
    }
    else
    {
        Console.WriteLine("❌ <EMAIL> user not found!");
    }
    reader2.Close();

    Console.WriteLine();
    Console.WriteLine("=== All users in database ===");

    // List all users
    var listUsers = new MySqlCommand("SELECT UserId, FirstName, LastName, Email FROM User ORDER BY UserId", connection);
    using var reader3 = listUsers.ExecuteReader();

    bool hasUsers = false;
    while (reader3.Read())
    {
        hasUsers = true;
        Console.WriteLine($"   ID: {reader3["UserId"]}, Name: {reader3["FirstName"]} {reader3["LastName"]}, Email: {reader3["Email"]}");
    }

    if (!hasUsers)
    {
        Console.WriteLine("❌ No users found in database!");
    }
    reader3.Close();

    Console.WriteLine();
    Console.WriteLine("=== User Organization Mappings ===");

    // Check user organization mappings for ***** users
    var checkMappings = new MySqlCommand(@"
        SELECT uom.MappingId, uom.UserId, u.Email, uom.OrganizationId, uom.RoleId, uom.IsActive, uom.IsApproved
        FROM User_Organization_Mapping uom
        JOIN User u ON uom.UserId = u.UserId
        WHERE u.Email IN ('*****@localhost', '<EMAIL>')
        ORDER BY u.Email, uom.OrganizationId", connection);

    using var reader4 = checkMappings.ExecuteReader();

    bool hasMappings = false;
    while (reader4.Read())
    {
        hasMappings = true;
        Console.WriteLine($"   Email: {reader4["Email"]}, OrgId: {reader4["OrganizationId"]}, RoleId: {reader4["RoleId"]}, Active: {reader4["IsActive"]}, Approved: {reader4["IsApproved"]}");
    }

    if (!hasMappings)
    {
        Console.WriteLine("❌ No organization mappings found for ***** users!");
        Console.WriteLine("   This could cause authentication to fail even with correct password.");
    }
}
catch (Exception ex)
{
    Console.WriteLine($"❌ Database connection failed: {ex.Message}");
    Console.WriteLine();
    Console.WriteLine("Possible issues:");
    Console.WriteLine("1. MySQL server is not running");
    Console.WriteLine("2. Database 'myadaptivecloud' doesn't exist");
    Console.WriteLine("3. User 'myac' doesn't have access");
    Console.WriteLine("4. Password is incorrect");
    Console.WriteLine("5. Connection string is wrong");
    Console.WriteLine();
    Console.WriteLine("To fix database issues:");
    Console.WriteLine("- Check if MySQL/MariaDB is running");
    Console.WriteLine("- Run database seeding scripts");
    Console.WriteLine("- Check connection string in appsettings.Development.json");
}

Console.WriteLine();
Console.WriteLine("=== Summary & Recommendations ===");
Console.WriteLine("Based on the SQL scripts in your codebase:");
Console.WriteLine("1. The correct password should be '*****' (not '123456')");
Console.WriteLine("2. The user should be '*****@localhost' (not '<EMAIL>')");
Console.WriteLine("3. The user needs proper organization mappings to authenticate");
Console.WriteLine("4. The user must be active (IsActive = 1)");
Console.WriteLine();
Console.WriteLine("Press any key to exit...");
Console.ReadKey();
