import { DestroyRef, Injectable, Injector, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Router } from '@angular/router';
import { ProvisioningCloudInfraService } from '@app/modules/provisioning/modules/cloud-infra/services/provisioning-cloud-infra.service';
import { ACUsageLimitModel } from '@app/shared/models/adaptivecloud/ac-usage-limit.model';
import { ConfirmationDialogModel } from '@app/shared/models/confirmation-dialog.model';
import { CloudInfrastructureValidationRequest } from '@app/shared/models/registration/cloud-Infrastructure-validation.request';
import { RegisterProvisioningFlowEnum } from '@app/shared/models/registration/register-provisioning.enum';
import { AuthService } from '@app/shared/services/auth.service';
import { MenuService } from '@app/shared/services/menu.service';
import { ModalService } from '@app/shared/services/modal.service';
import { RegistrationService } from '@app/shared/services/registration.service';
import { UserContextService } from '@app/shared/services/user-context.service';
import { isOrganizationDefined } from '@app/shared/utils/helpers';
import { CreateCloudInfrastructureComponent } from '../components/create-cloud-infrastructure/create-cloud-infrastructure.component';
import { CreateOrganizationComponent } from '../components/create-organization/create-organization.component';
import { CreateRegistrationInvitationCodeComponent } from '../components/create-registration-invitation-code/create-registration-invitation-code.component';
import { RegistrationCloudInfrastructureReadyComponent } from '../components/registration-cloudinfrastructure-ready/registration-cloudinfrastructure-ready.component';
import { SelectOrganizationComponent } from '../components/select-organization/select-organization.component';
import { RegistrationFlowModel } from '../models/registration-flow.model';

@Injectable({
    providedIn: 'root'
})
export class RegistrationFlowService {
    private readonly registrationService = inject(RegistrationService);
    private readonly userContextService = inject(UserContextService);
    private readonly modalService = inject(ModalService);
    private readonly authService = inject(AuthService);
    private readonly router = inject(Router);
    private readonly injector = inject(Injector);
    private readonly menuService = inject(MenuService);
    private readonly destroyRef = inject(DestroyRef);

    public registrationFlowModel = new RegistrationFlowModel();

    public initRegistrationFlow() {
        // Skip invitation code prompt and go directly to company registration flow
        this.registrationService.getCompaniesByLinkedEmail()
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(res => {
                this.registrationFlowModel.companies = res.data;
                if (res.data?.length) {
                    this.openRegisterExistingCompanyModal();
                } else {
                    this.openRegisterNewCompanyModal();
                }
            });
    }

    public openCreateCloudInfrastructureConfirmationModal(modalData?: ConfirmationDialogModel, provisioningFlow?: RegisterProvisioningFlowEnum): void {
        let confirmationModal;
        if (modalData) {
            confirmationModal = this.modalService.openConfirmationDialog(modalData);
        } else {
            confirmationModal = this.modalService.openConfirmationDialog({
                title: 'Cloud Infrastructure account',
                content: 'Would you like to create a Cloud Infrastructure account?',
                showCancelButton: true,
                cancelButtonText: 'No, maybe later',
                confirmButtonText: 'Yes'
            });
        }

        if (!provisioningFlow) {
            provisioningFlow = RegisterProvisioningFlowEnum.None;
        }

        confirmationModal.closed
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(createCloudInfrastructureConfirmationResult => {
                if (createCloudInfrastructureConfirmationResult) {
                    const request: CloudInfrastructureValidationRequest = {
                        provisioningFlow
                    };
                    this.registrationService.cloudInfrastructureValidationAndMapping(this.registrationFlowModel.organizationId, request)
                        .pipe(takeUntilDestroyed(this.destroyRef))
                        .subscribe(cloudInfrastructureValidationAndMappingResult => {
                            if (cloudInfrastructureValidationAndMappingResult.data.redirectToCreateDomain) {
                                this.userContextService.initializeUserContext().then(() => {

                                    const modalRef = this.modalService.openModalComponent(CreateCloudInfrastructureComponent);
                                    if (provisioningFlow === RegisterProvisioningFlowEnum.SubOrg && this.registrationFlowModel.organizationId) {
                                        (modalRef.componentInstance as CreateCloudInfrastructureComponent).provisioningFlow = provisioningFlow;
                                        (modalRef.componentInstance as CreateCloudInfrastructureComponent).domainOrganizationId = this.registrationFlowModel.organizationId;
                                    }

                                    modalRef.closed
                                        .pipe(takeUntilDestroyed(this.destroyRef))
                                        .subscribe(cloudInfrastructureResult => {
                                            this.showFlowResultMessageAndRedirectToHome(
                                                cloudInfrastructureResult.data.messages,
                                                cloudInfrastructureResult.data.resourceLimits,
                                                provisioningFlow
                                            );
                                        });
                                });
                            } else {
                                this.showFlowResultMessageAndRedirectToHome(
                                    cloudInfrastructureValidationAndMappingResult.data.messages,
                                    cloudInfrastructureValidationAndMappingResult.data.resourceLimits,
                                    provisioningFlow
                                );
                            }
                        });

                } else {
                    this.showFlowResultMessageAndRedirectToHome([this.registrationFlowModel.message, 'You now have access to MyAdaptiveCloud.'], null, provisioningFlow);
                }
            });
    }

    public openRegisterExistingCompanyModal(): void {
        const selectExistingOrganizationModal = this.modalService.openModalComponent(SelectOrganizationComponent);
        (selectExistingOrganizationModal.componentInstance as SelectOrganizationComponent).registrationFlowModel = this.registrationFlowModel;

        selectExistingOrganizationModal.dismissed
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(() => {
                this.openRegisterNewCompanyModal();
            });

        selectExistingOrganizationModal.closed
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(() => {
                this.registrationService.hasOrganizationMapping(this.registrationFlowModel.companyId)
                    .pipe(takeUntilDestroyed(this.destroyRef))
                    .subscribe(hasOrganizationMapping => {
                        if (hasOrganizationMapping.data) {
                            this.registrationService.registerExistingCompany(this.registrationFlowModel.companyId)
                                .pipe(takeUntilDestroyed(this.destroyRef))
                                .subscribe(res => {
                                    this.registrationFlowModel.message = res.message;
                                    this.registrationFlowModel.organizationId = res.data.organizationId;
                                    this.showFlowResultMessageAndRedirectToHome([this.registrationFlowModel.message, 'You now have access to MyAdaptiveCloud.']);
                                });
                        } else {
                            this.openCompanyRegistrationModal();
                        }
                    });

            });
    }

    public openRegisterNewCompanyModal(): void {
        const createNewOrganizationModal = this.modalService.openModalComponent(CreateOrganizationComponent);
        (createNewOrganizationModal.componentInstance as CreateOrganizationComponent).registrationFlowModel = this.registrationFlowModel;

        createNewOrganizationModal.dismissed
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(() => {
                if (this.registrationFlowModel.companies?.length) {
                    this.openRegisterExistingCompanyModal();
                } else {
                    this.openRegisterNewCompanyModal();
                }
            });

        createNewOrganizationModal.closed
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(() => {
                this.openCompanyRegistrationModal();
            });

    }

    public openCompanyRegistrationModal(): void {
        if (this.registrationFlowModel.companyId) {
            this.registrationService.registerExistingCompany(this.registrationFlowModel.companyId)
                .pipe(takeUntilDestroyed(this.destroyRef))
                .subscribe(res => {
                    this.registrationFlowModel.message = res.message;
                    this.registrationFlowModel.organizationId = res.data.organizationId;
                    this.showFlowResultMessageAndRedirectToHome([this.registrationFlowModel.message, 'You now have access to MyAdaptiveCloud.'], null, RegisterProvisioningFlowEnum.None);
                });
        } else if (this.registrationFlowModel.newOrganization !== null) {
            this.registrationService.registerNewCompany(this.registrationFlowModel.newOrganization)
                .pipe(takeUntilDestroyed(this.destroyRef))
                .subscribe(res => {
                    this.registrationFlowModel.message = res.message;
                    this.registrationFlowModel.organizationId = res.data.organizationId;
                    this.showFlowResultMessageAndRedirectToHome([this.registrationFlowModel.message, 'You now have access to MyAdaptiveCloud.'], null, RegisterProvisioningFlowEnum.None);
                });
        }
    }

    public showFlowResultMessageAndRedirectToHome(messages?: string[], resourceLimits?: ACUsageLimitModel[], provisioningFlow?: RegisterProvisioningFlowEnum): void {
        const isError = messages[0] === 'Error';
        // RegistrationCloudInfrastructureReadyComponent only should appears when self registered or Error
        if (messages?.every(a => a) && (isError || (provisioningFlow === RegisterProvisioningFlowEnum.None || provisioningFlow === RegisterProvisioningFlowEnum.RootOrg))) {

            const modalRef = this.modalService.openModalComponent(RegistrationCloudInfrastructureReadyComponent);
            // set title to the first message in messages array and remove it from the array
            const registrationCloudInfrustructureReadyModal = modalRef.componentInstance as RegistrationCloudInfrastructureReadyComponent;
            registrationCloudInfrustructureReadyModal.title = messages?.shift() ?? '';
            registrationCloudInfrustructureReadyModal.messages = messages;
            registrationCloudInfrustructureReadyModal.resourceLimits = resourceLimits;
            modalRef.closed
                .pipe(takeUntilDestroyed(this.destroyRef))
                .subscribe(() => {
                    // If error no redirect.
                    if (!isError) {
                        this.redirectUserByFlow(provisioningFlow);
                    }
                });
        } else if (messages?.every(a => a)) { // If NO messages no redirect.
            this.redirectUserByFlow(provisioningFlow);
        }
    }

    redirectUserByFlow(provisioningFlow?: RegisterProvisioningFlowEnum) {
        if (provisioningFlow === RegisterProvisioningFlowEnum.RootOrg) {
            this.menuService.reloadMenuItems();
            this.router.navigate(['/provisioning']);
        } else if (provisioningFlow === RegisterProvisioningFlowEnum.SubOrg) {
            this.menuService.reloadMenuItems();
            const provisioningCloudInfraService = this.injector.get(ProvisioningCloudInfraService);
            provisioningCloudInfraService.refreshProvisioning();
        } else {
            this.userContextService.initializeUserContext().then(() => {
                this.authService.redirectToSSOLogin(false);
            });
        }
    }
}
