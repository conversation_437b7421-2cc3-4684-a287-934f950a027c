using MySqlConnector;

string connectionString = "Server=localhost,3306;User Id=myac;Password=*****;Database=myadaptivecloud;";

try
{
    using var connection = new MySqlConnection(connectionString);
    connection.Open();
    Console.WriteLine("🔧 Creating missing configuration_value_organization table...");
    Console.WriteLine();

    // Create configuration_value_organization table
    var createTableCmd = new MySqlCommand(@"
        CREATE TABLE IF NOT EXISTS `configuration_value_organization` (
            `ConfigurationValueOrganizationId` INT NOT NULL AUTO_INCREMENT,
            `Value` TEXT NOT NULL,
            `OrganizationId` INT NOT NULL,
            `CreatedBy` INT NOT NULL,
            `UpdatedBy` INT,
            `CreatedOn` DATETIME NOT NULL,
            `UpdatedOn` DATETIME,
            `ConfigurationValuesId` INT NOT NULL,
            PRIMARY KEY (`ConfigurationValueOrganizationId`),
            UNIQUE INDEX `OrganizationId_ConfigurationValuesId` (`OrganizationId`, `ConfigurationValuesId`),
            CONSTRAINT `fk_configuration_value_organization_organizationId` FOREIGN KEY (`OrganizationId`) REFERENCES `Organization` (`OrganizationId`),
            CONSTRAINT `fk_configuration_value_organization_configurationValuesId` FOREIGN KEY (`ConfigurationValuesId`) REFERENCES `ConfigurationValues` (`ConfigurationValuesId`)
        )", connection);

    createTableCmd.ExecuteNonQuery();
    Console.WriteLine("✅ configuration_value_organization table created!");

    // Get default user ID
    var getUserCmd = new MySqlCommand(@"
        SELECT COALESCE(
            (SELECT UserId FROM User WHERE Email = '*****@localhost' LIMIT 1),
            (SELECT UserId FROM User WHERE Email = '<EMAIL>' LIMIT 1),
            (SELECT MIN(UserId) FROM User WHERE UserId IS NOT NULL)
        ) AS DefaultUserId", connection);

    var defaultUserId = getUserCmd.ExecuteScalar();
    Console.WriteLine($"Default User ID: {defaultUserId}");

    // Get default organization ID
    var getOrgCmd = new MySqlCommand(@"
        SELECT COALESCE(
            (SELECT OrganizationId FROM Organization WHERE OrganizationId = 0 LIMIT 1),
            (SELECT MIN(OrganizationId) FROM Organization WHERE OrganizationId IS NOT NULL),
            0
        ) AS DefaultOrgId", connection);

    var defaultOrgId = getOrgCmd.ExecuteScalar();
    Console.WriteLine($"Default Organization ID: {defaultOrgId}");

    // Populate configuration_value_organization with existing ConfigurationValues
    var populateCmd = new MySqlCommand($@"
        INSERT IGNORE INTO configuration_value_organization (ConfigurationValuesId, OrganizationId, Value, CreatedBy, UpdatedBy, CreatedOn, UpdatedOn)
        SELECT
            ConfigurationValuesId,
            {defaultOrgId} AS OrganizationId,
            Value,
            COALESCE(UpdatedBy, {defaultUserId}, 1) AS CreatedBy,
            UpdatedBy,
            COALESCE(UpdatedDate, UTC_TIMESTAMP()) AS CreatedOn,
            UpdatedDate
        FROM
            ConfigurationValues", connection);

    var recordsInserted = populateCmd.ExecuteNonQuery();
    Console.WriteLine($"✅ {recordsInserted} configuration records populated!");

    Console.WriteLine();
    Console.WriteLine("✅ Missing tables created successfully!");
}
catch (Exception ex)
{
    Console.WriteLine($"❌ Error: {ex.Message}");
    Console.WriteLine($"Stack trace: {ex.StackTrace}");
}

Console.WriteLine();
Console.WriteLine("Press any key to exit...");
Console.ReadKey();
